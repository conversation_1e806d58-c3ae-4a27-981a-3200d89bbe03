# Project Requirements Document: BMAD Studio

## 1. Introduction
### 1.1. Purpose
This document outlines the requirements and design for the BMAD Studio base application, focusing on its modular, API-driven architecture, .NET backend, and Blazor frontend. It serves as a comprehensive guide for development, ensuring all stakeholders have a clear understanding of the project's scope, features, and technical considerations.

### 1.2. <PERSON>ope
The scope of this document covers the initial development and architectural decisions for the BMAD Studio, including project setup, core API functionalities, frontend components, and the migration from Blazor WebAssembly to Blazor Server. It also addresses environmental settings and lessons learned from initial troubleshooting.

### 1.3. Target Audience
This document is intended for developers, architects, project managers, quality assurance engineers, and other stakeholders involved in the design, development, and deployment of the BMAD Studio application.

## 2. Project Overview

### 2.1. High-Level Description
The BMAD Studio is a base application designed with a modular, API-driven architecture. It features a .NET backend and a Blazor WebAssembly (initially) / Blazor Server (subsequently) frontend. The application aims to provide a robust and scalable platform for various studio functionalities.

### 2.2. Goals and Objectives
- To establish a foundational application structure using Clean Architecture principles.
- To implement a clear separation of concerns between the backend API and frontend UI.
- To provide secure authentication using JWT.
- To develop a modular and extensible system capable of integrating various "hub" functionalities (e.g., Process, Configuration, Design, Project, Analytics, Knowledge).
- To ensure maintainability and scalability through well-defined project layers.
- To adapt to user preferences by migrating from Blazor WebAssembly to Blazor Server for improved performance and development experience.

## 3. Architecture and Design

### 3.1. Overall Architecture (Clean Architecture)
The BMAD Studio solution is structured around Clean Architecture principles, promoting a clear separation of concerns, testability, and maintainability. The solution is composed of the following projects:
- `BMAD.Studio.Api`: The entry point for the backend, exposing RESTful APIs.
- `BMAD.Studio.Application`: Contains application-specific business rules and orchestrates data flow.
- `BMAD.Studio.Domain`: Encapsulates enterprise-wide business rules and entities.
- `BMAD.Studio.Infrastructure`: Handles external concerns such as data access, external services, and file systems.
- `BMAD.Studio.Shared`: Contains common models, DTOs, and utilities shared across projects.
- `BMAD.Studio.Web` (initially Blazor WebAssembly, later replaced by `BMAD.Studio.Server`): The frontend application.

### 3.2. API-Driven Approach
The frontend communicates with the backend exclusively through well-defined RESTful APIs. This approach ensures a decoupled system, allowing independent development and deployment of frontend and backend components.

### 3.3. Key Design Decisions
- **Modular Design**: The application is designed with modularity in mind, allowing for easy addition of new features and "hubs" without significantly impacting existing functionalities.
- **Authentication**: JSON Web Tokens (JWT) are used for secure authentication, providing a stateless and scalable authentication mechanism.
- **Dashboard UI**: A card-based dashboard UI is implemented for an intuitive and organized user experience, providing quick access to different hub functionalities.
- **Scalability**: The Clean Architecture and API-driven design contribute to a scalable solution, capable of handling future growth and increased complexity.

## 4. Key Features

### 4.1. Backend API
The backend API (`BMAD.Studio.Api`) provides the core functionalities and data services for the application. Key controllers and their responsibilities include:
- **`AuthController`**: Handles user authentication and authorization, including user registration and login, issuing and validating JWTs.
- **`HubsController`**: Manages the various "hubs" within the application, providing endpoints for interacting with different studio functionalities.
- **`ConfigurationController`**: Manages application-wide configurations and settings.
- **Initial Data Setup**: Hardcoded data is used for initial development and setup, to be replaced by persistent storage (e.g., Entity Framework Core) in future iterations.

### 4.2. Frontend (Blazor WebAssembly/Server)
The frontend application (`BMAD.Studio.Web` / `BMAD.Studio.Server`) provides the user interface and interacts with the backend API. Key components and pages include:
- **`HubCard` Component**: A reusable UI component for displaying and navigating to different hubs on the dashboard.
- **`Dashboard` Page**: The main landing page, presenting an overview of available hubs.
- **`Login` and `Register` Pages**: User authentication interfaces.
- **`HubLayout`**: A shared layout for all hub-related pages, ensuring consistent navigation and branding.
- **Placeholder Hub Pages**: Initial placeholder pages for various hubs, including `ProcessHub`, `ConfigurationHub`, `DesignHub`, `ProjectHub`, `AnalyticsHub`, and `KnowledgeHub`, to be developed with specific functionalities.

## 5. Technical Stack and Dependencies

### 5.1. Backend Technologies
- **ASP.NET Core**: The primary framework for building the backend API, leveraging its robust features for web application development.

### 5.2. Frontend Technologies
- **Blazor WebAssembly** (initial): Used for the initial frontend development, offering client-side web UI with .NET.
- **Blazor Server** (migrated to): The chosen framework for the migrated frontend, providing server-side rendering and improved performance for certain scenarios.

### 5.3. Libraries and Frameworks
- **Entity Framework Core** (planned): An ORM for data access, planned for future implementation to manage database interactions.
- **JWT Bearer**: Used for implementing JSON Web Token-based authentication and authorization.
- **Swagger/OpenAPI**: For API documentation and testing, providing an interactive interface to explore API endpoints.
- **HttpClient**: Used in the frontend for making HTTP requests to the backend API.

## 6. Environmental Settings

### 6.1. Configuration
- **JWT Settings**: JSON Web Token (JWT) configuration details (e.g., secret key, issuer, audience, expiration) are managed within `appsettings.json` files, allowing for environment-specific configurations.
- **CORS Policy**: During development, an open Cross-Origin Resource Sharing (CORS) policy is implemented to facilitate seamless communication between the frontend and backend. This policy will be restricted for production environments to enhance security.

### 6.2. Deployment Considerations
- The modular architecture supports flexible deployment options for the API and Blazor Server applications.
- Environment-specific `appsettings.json` files will be utilized to manage configurations for different deployment environments (e.g., Development, Staging, Production).

## 7. Migration Details

### 7.1. Blazor WebAssembly to Blazor Server Migration
Initially, the frontend was developed using Blazor WebAssembly. Due to user preference and potential benefits in certain scenarios (e.g., faster initial load times, reduced client-side processing), a decision was made to migrate the frontend to Blazor Server. This migration involved:
- **Creation of `BMAD.Studio.Server` Project**: A new Blazor Server project was created to host the migrated frontend.
- **Shared Project References**: Necessary references to shared projects (e.g., `BMAD.Studio.Shared`) were added to the new server project.
- **Component and Page Migration**: Existing Blazor components, pages, and static assets from `BMAD.Studio.Web` were planned for migration to the `BMAD.Studio.Server` project.
- **Solution File Update**: The `BMAD.Studio.Web` project was removed from the solution file, and `BMAD.Studio.Server` was added to reflect the change.

### 7.2. Rationale for Migration
The primary rationale for migrating to Blazor Server was to align with user preferences and potentially leverage server-side rendering benefits, such as:
- **Faster Initial Load**: For applications with large bundles, Blazor Server can provide a faster initial load time as the UI is rendered on the server.
- **Reduced Client-Side Requirements**: Less processing is required on the client-side, which can be beneficial for users with lower-end devices.
- **Direct Access to Server Resources**: Blazor Server applications can directly access server-side resources and APIs without additional API calls.

## 8. Troubleshooting and Solution Updates

### 8.1. Past Challenges and Resolutions
During the initial setup and migration phases, several challenges were encountered and resolved:
- **Deprecated Blazor Server Template**: The `dotnet new blazorserver` command was found to be deprecated.
    - **Resolution**: The `dotnet new blazor` command was used instead, which provides a more up-to-date Blazor project template. Manual guidance was then provided for migrating files.
- **`dotnet add reference` Path Issues**: Problems were encountered when adding project references using `dotnet add reference` due to incorrect paths.
    - **Resolution**: Manual adjustments to project file references or careful path specification were required to correctly link projects.

### 8.2. Lessons Learned
- **Template Evolution**: Always verify the latest and recommended `dotnet` templates for new project creation.
- **Path Sensitivity**: Pay close attention to file paths when adding project references, especially in a multi-project solution.
- **Iterative Migration**: For significant architectural changes like migrating between Blazor hosting models, an iterative approach with clear steps and verification points is crucial.

## 9. Future Considerations (Optional)
- Integration with a persistent database (e.g., SQL Server with Entity Framework Core).
- Implementation of comprehensive logging and monitoring.
- Development of specific functionalities for each "hub."
- Enhanced error handling and user feedback mechanisms.
- CI/CD pipeline setup for automated deployments.

## 10. Appendices (Optional)
- Glossary of Terms
- References
