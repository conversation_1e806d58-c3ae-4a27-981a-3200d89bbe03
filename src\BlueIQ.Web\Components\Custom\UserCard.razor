@using BlueIQ.Api.Models

<div class="card mb-3">
    <div class="card-body">
        <h5 class="card-title">@User.FirstName @User.LastName</h5>
        <p class="card-text">
            <i class="bi bi-envelope"></i> @User.Email
        </p>
        <small class="text-muted">
            <i class="bi bi-calendar"></i> Member since @User.CreatedAt.ToString("MMMM yyyy")
        </small>
    </div>
</div>

@code {
    [Parameter, EditorRequired]
    public UserDto User { get; set; } = new();
    
    // This establishes your component pattern for agents to follow
}
