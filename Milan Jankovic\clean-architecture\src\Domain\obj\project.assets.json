{"version": 3, "targets": {"net9.0": {"SonarAnalyzer.CSharp/10.9.0.115408": {"type": "package"}, "SharedKernel/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v9.0", "compile": {"bin/placeholder/SharedKernel.dll": {}}, "runtime": {"bin/placeholder/SharedKernel.dll": {}}}}}, "libraries": {"SonarAnalyzer.CSharp/10.9.0.115408": {"sha512": "cLvuPNhLWTnHtbTQzZh64xao0ZNsXKc8oZ0Q4agAmMjnqG7Q54mL8jw55rYnVytv+cXRw9ItRoUsggnvRsKJIA==", "type": "package", "path": "sonaranalyzer.csharp/10.9.0.115408", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "analyzers/SonarAnalyzer.CSharp.dll", "images/sonarsource_64.png", "license/THIRD-PARTY-NOTICES.txt", "sonaranalyzer.csharp.10.9.0.115408.nupkg.sha512", "sonaranalyzer.csharp.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "SharedKernel/1.0.0": {"type": "project", "path": "../SharedKernel/SharedKernel.csproj", "msbuildProject": "../SharedKernel/SharedKernel.csproj"}}, "projectFileDependencyGroups": {"net9.0": ["SharedKernel >= 1.0.0", "SonarAnalyzer.CSharp >= 10.9.0.115408"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\_GithubRepos\\Milan <PERSON>\\clean-architecture\\src\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "C:\\_Dev\\_GithubRepos\\Milan <PERSON>\\clean-architecture\\src\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\_GithubRepos\\Milan <PERSON>ic\\clean-architecture\\src\\Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\_Dev\\_Nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj": {"projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.9.0.115408, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.4", "Microsoft.AspNetCore.OpenApi": "9.0.4", "Microsoft.EntityFrameworkCore": "9.0.4", "Microsoft.EntityFrameworkCore.Tools": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Scrutor": "6.0.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.9.0.115408", "Swashbuckle.AspNetCore": "8.1.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}}