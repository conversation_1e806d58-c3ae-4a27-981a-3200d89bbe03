is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = BlueIQ.Web
build_property.RootNamespace = BlueIQ.Web
build_property.ProjectDir = C:\_Dev\Projects\BlueIQ-BMAD\src\BlueIQ.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\_Dev\Projects\BlueIQ-BMAD\src\BlueIQ.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/Custom/UserCard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xDdXN0b21cVXNlckNhcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/Pages/Users.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xVc2Vycy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-y3aoxxumh7

[C:/_Dev/Projects/BlueIQ-BMAD/src/BlueIQ.Web/Components/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTmF2TWVudS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-bixx2r33e6
