using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using BlueIQ.Api.Services;
using BlueIQ.Api.Models;
using BlueIQ.Data;
using BlueIQ.Data.Entities;

namespace BlueIQ.Tests;

public class UserServiceTests
{
    private AppDbContext GetInMemoryContext()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        return new AppDbContext(options);
    }

    [Fact]
    public async Task GetUserByIdAsync_ExistingUser_ReturnsUserDto()
    {
        // Arrange
        using var context = GetInMemoryContext();
        var logger = new Mock<ILogger<UserService>>();
        var service = new UserService(context, logger.Object);

        var user = new User
        {
            Id = 1,
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        context.Users.Add(user);
        await context.SaveChangesAsync();

        // Act
        var result = await service.GetUserByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("<EMAIL>", result.Email);
        Assert.Equal("John", result.FirstName);
        Assert.Equal("Doe", result.LastName);
    }

    [Fact]
    public async Task GetUserByIdAsync_NonExistentUser_ReturnsNull()
    {
        // Arrange
        using var context = GetInMemoryContext();
        var logger = new Mock<ILogger<UserService>>();
        var service = new UserService(context, logger.Object);

        // Act
        var result = await service.GetUserByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task CreateUserAsync_ValidRequest_ReturnsUserDto()
    {
        // Arrange
        using var context = GetInMemoryContext();
        var logger = new Mock<ILogger<UserService>>();
        var service = new UserService(context, logger.Object);

        var request = new CreateUserRequest
        {
            Email = "<EMAIL>",
            FirstName = "Jane",
            LastName = "Smith"
        };

        // Act
        var result = await service.CreateUserAsync(request);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("<EMAIL>", result.Email);
        Assert.Equal("Jane", result.FirstName);
        Assert.Equal("Smith", result.LastName);
        Assert.True(result.Id > 0);

        // Verify user was saved to database
        var savedUser = await context.Users.FirstOrDefaultAsync(u => u.Id == result.Id);
        Assert.NotNull(savedUser);
        Assert.Equal(request.Email, savedUser.Email);
    }

    [Fact]
    public async Task UpdateUserAsync_ExistingUser_ReturnsUpdatedUserDto()
    {
        // Arrange
        using var context = GetInMemoryContext();
        var logger = new Mock<ILogger<UserService>>();
        var service = new UserService(context, logger.Object);

        var user = new User
        {
            Id = 1,
            Email = "<EMAIL>",
            FirstName = "Original",
            LastName = "User",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        context.Users.Add(user);
        await context.SaveChangesAsync();

        var updateRequest = new CreateUserRequest
        {
            Email = "<EMAIL>",
            FirstName = "Updated",
            LastName = "User"
        };

        // Act
        var result = await service.UpdateUserAsync(1, updateRequest);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("<EMAIL>", result.Email);
        Assert.Equal("Updated", result.FirstName);
        Assert.Equal("User", result.LastName);
    }

    [Fact]
    public async Task UpdateUserAsync_NonExistentUser_ReturnsNull()
    {
        // Arrange
        using var context = GetInMemoryContext();
        var logger = new Mock<ILogger<UserService>>();
        var service = new UserService(context, logger.Object);

        var updateRequest = new CreateUserRequest
        {
            Email = "<EMAIL>",
            FirstName = "Test",
            LastName = "User"
        };

        // Act
        var result = await service.UpdateUserAsync(999, updateRequest);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task DeleteUserAsync_ExistingUser_ReturnsTrue()
    {
        // Arrange
        using var context = GetInMemoryContext();
        var logger = new Mock<ILogger<UserService>>();
        var service = new UserService(context, logger.Object);

        var user = new User
        {
            Id = 1,
            Email = "<EMAIL>",
            FirstName = "Delete",
            LastName = "Me",
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };
        context.Users.Add(user);
        await context.SaveChangesAsync();

        // Act
        var result = await service.DeleteUserAsync(1);

        // Assert
        Assert.True(result);

        // Verify user was deleted from database
        var deletedUser = await context.Users.FirstOrDefaultAsync(u => u.Id == 1);
        Assert.Null(deletedUser);
    }

    [Fact]
    public async Task DeleteUserAsync_NonExistentUser_ReturnsFalse()
    {
        // Arrange
        using var context = GetInMemoryContext();
        var logger = new Mock<ILogger<UserService>>();
        var service = new UserService(context, logger.Object);

        // Act
        var result = await service.DeleteUserAsync(999);

        // Assert
        Assert.False(result);
    }
    
    // This establishes your testing pattern for agents to follow
}
