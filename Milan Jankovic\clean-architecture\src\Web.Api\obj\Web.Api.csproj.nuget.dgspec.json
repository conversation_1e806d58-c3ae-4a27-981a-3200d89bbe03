{"format": 1, "restore": {"C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Web.Api\\Web.Api.csproj": {}}, "projects": {"C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Application\\Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\_GithubRepos\\Milan Jo<PERSON>ic\\clean-architecture\\src\\Application\\Application.csproj", "projectName": "Application", "projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jo<PERSON>ic\\clean-architecture\\src\\Application\\Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\_GithubRepos\\Milan Jo<PERSON>ic\\clean-architecture\\src\\Application\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\_Dev\\_Nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Domain\\Domain.csproj": {"projectPath": "C:\\_Dev\\_GithubRepos\\Milan <PERSON>\\clean-architecture\\src\\Domain\\Domain.csproj"}, "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj": {"projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[12.0.0, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.4, )", "versionCentrallyManaged": true}, "Scrutor": {"target": "Package", "version": "[6.0.1, )", "versionCentrallyManaged": true}, "Serilog": {"target": "Package", "version": "[4.2.0, )", "versionCentrallyManaged": true}, "SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.9.0.115408, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.4", "Microsoft.AspNetCore.OpenApi": "9.0.4", "Microsoft.EntityFrameworkCore": "9.0.4", "Microsoft.EntityFrameworkCore.Tools": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Scrutor": "6.0.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.9.0.115408", "Swashbuckle.AspNetCore": "8.1.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Domain\\Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\_GithubRepos\\Milan <PERSON>\\clean-architecture\\src\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "C:\\_Dev\\_GithubRepos\\Milan <PERSON>\\clean-architecture\\src\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\_GithubRepos\\Milan <PERSON>ic\\clean-architecture\\src\\Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\_Dev\\_Nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj": {"projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.9.0.115408, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.4", "Microsoft.AspNetCore.OpenApi": "9.0.4", "Microsoft.EntityFrameworkCore": "9.0.4", "Microsoft.EntityFrameworkCore.Tools": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Scrutor": "6.0.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.9.0.115408", "Swashbuckle.AspNetCore": "8.1.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Infrastructure\\Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Infrastructure\\Infrastructure.csproj", "projectName": "Infrastructure", "projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Infrastructure\\Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Infrastructure\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\_Dev\\_Nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Application\\Application.csproj": {"projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jo<PERSON>ic\\clean-architecture\\src\\Application\\Application.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNetCore.HealthChecks.NpgSql": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "EFCore.NamingConventions": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.4, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[9.0.4, )", "versionCentrallyManaged": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )", "versionCentrallyManaged": true}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )", "versionCentrallyManaged": true}, "SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.9.0.115408, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.4", "Microsoft.AspNetCore.OpenApi": "9.0.4", "Microsoft.EntityFrameworkCore": "9.0.4", "Microsoft.EntityFrameworkCore.Tools": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Scrutor": "6.0.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.9.0.115408", "Swashbuckle.AspNetCore": "8.1.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj", "projectName": "SharedKernel", "projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\SharedKernel.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\SharedKernel\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\_Dev\\_Nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.9.0.115408, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.4", "Microsoft.AspNetCore.OpenApi": "9.0.4", "Microsoft.EntityFrameworkCore": "9.0.4", "Microsoft.EntityFrameworkCore.Tools": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Scrutor": "6.0.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.9.0.115408", "Swashbuckle.AspNetCore": "8.1.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Web.Api\\Web.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\_GithubRepos\\Milan Jo<PERSON>ovic\\clean-architecture\\src\\Web.Api\\Web.Api.csproj", "projectName": "Web.Api", "projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jo<PERSON>ovic\\clean-architecture\\src\\Web.Api\\Web.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\_GithubRepos\\Milan Jo<PERSON>ic\\clean-architecture\\src\\Web.Api\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "C:\\_Dev\\_Nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Infrastructure\\Infrastructure.csproj": {"projectPath": "C:\\_Dev\\_GithubRepos\\Milan Jovanovic\\clean-architecture\\src\\Infrastructure\\Infrastructure.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNetCore.HealthChecks.UI.Client": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.4, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )", "versionCentrallyManaged": true}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )", "versionCentrallyManaged": true}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "Serilog.Sinks.Seq": {"target": "Package", "version": "[9.0.0, )", "versionCentrallyManaged": true}, "SonarAnalyzer.CSharp": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[10.9.0.115408, )", "versionCentrallyManaged": true}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.1, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"AspNetCore.HealthChecks.NpgSql": "9.0.0", "AspNetCore.HealthChecks.UI.Client": "9.0.0", "coverlet.collector": "6.0.4", "EFCore.NamingConventions": "9.0.0", "FluentValidation.DependencyInjectionExtensions": "12.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.4", "Microsoft.AspNetCore.OpenApi": "9.0.4", "Microsoft.EntityFrameworkCore": "9.0.4", "Microsoft.EntityFrameworkCore.Tools": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.2", "NetArchTest.Rules": "1.3.2", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "Scrutor": "6.0.1", "Serilog": "4.2.0", "Serilog.AspNetCore": "9.0.0", "Serilog.Sinks.Seq": "9.0.0", "Shouldly": "4.3.0", "SonarAnalyzer.CSharp": "10.9.0.115408", "Swashbuckle.AspNetCore": "8.1.1", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.1.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}}}