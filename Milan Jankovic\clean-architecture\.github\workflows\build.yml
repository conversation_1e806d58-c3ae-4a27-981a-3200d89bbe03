name: Build

on:
  workflow_dispatch:
  push:
    branches:
      - main

env:
  DOTNET_VERSION: "9.x"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Restore
        run: dotnet restore CleanArchitecture.sln

      - name: Build
        run: dotnet build CleanArchitecture.sln --configuration Release --no-restore

      - name: Test
        run: dotnet test CleanArchitecture.sln --configuration Release --no-restore --no-build

      - name: Publish
        run: dotnet publish CleanArchitecture.sln --configuration Release --no-restore --no-build
