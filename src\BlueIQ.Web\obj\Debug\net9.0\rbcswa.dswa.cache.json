{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["s2qEi1RJ3EUbladsdBz4q+e0lJ5dFpcOc6sxHB2R1SU=", "jDRZTgTACTtzOpT2ze8/dlMbGncjZ2IDyFx1VAsq+a4=", "rrEm+gFADZ1xpj2WHarc04zmY9D7A8gHaQu70Ug7n9Q=", "BO3j/ydVLHVPYB7WQkUK27+tWI2ZRfW0t6hxcQN0FFo=", "NCg3Ky39e5rhLISfzwBmtYigDJdTd3mcoz0531ZfsuY=", "VczgRVCdFnbtQrNhInHWyOeTf5+R/zcBRYX+IVQoy7c=", "x0FYM4AMKAHCMOjzRpDvGiH+VF7GfQzrFuY30+IH0tE=", "tzCsDvTwZvS3x014P5UF0WPR2BWAERweYJcoTI+FM0c=", "KOEkp7LDA12FKIQCuYbnCKmqss7YeaPBNCjKd/T0lTA=", "39ieHgRGDq+py5GJDyz8+2viH/RF1ZnIkzlSrIInwSM=", "jhfG11kXZTAMpyj0l9/Z2nGDeSB9qSxeLSZcSPS+4R4=", "5sv1VI73BarRnvc/sPbcGNNtQ1mbz4SDw2o3SbuwCfg=", "FhsRn1Nt1zmLqCIffFxpzomYL8YD3s+4jVv5vEoWnj4=", "5SgSt2hH/q0LncHz0EPTFscxrnh6imKNOebL1t+wMRs=", "JJiJ6QvPLkCyaoZNybrUXGkEgbXyTdE+t5B605tWNXQ=", "aF+7nWfZ0AGZOjGQOAZqBfkmXfuBvh2vaYAKXzNIo8A=", "I15D9R9RiCu/RqpjP9PopIWqVwN18DGVAUaxGMyP0Qo=", "9wZnHZIt0AWeMy276zevOWAJPP542Xv9uGioQcfLmmI=", "687gpQ/3X47yrJ4TgtdnCcPtAwRtS6u1/WWxqBAtwUo=", "DvH7F2gUlVK7j5vryy1+mxWgJJFexuUPbhgZb9C4t2M=", "ZIy76R3KJ1ucSfdN3z05G5QRGLLtTeNptWzr+bhoz00=", "JcI1sZGb5yGWounLafwSOG1smG4Zx3nVCOZj2+71o4Y=", "KMnSdABkWoOmYbJZS7/8g6RuzpHhXiUu6NVGYVK1d/Q=", "dZOZCHziptVzIFMdpdGefSAhc8Hf5by/mVVXFgJSBgw=", "4y/26zwWNxKAmekP0ULpV+LJxaD5PwP9eRqF8nzP6No=", "InHhB16R83NelO8spAbzRU0RIzZ+9kbQdlbZfigM5XU=", "2KPCrAaeoGPDvvdqMc1Inqd2OZlJ0oKIUrP6sUNohzQ=", "AF41EuusBXiCzPekckR5+2j3EcyBScLnLM2TcL7Jbrg=", "Zhqsf7rMoGVC1Z0GSumW/cLNItF/em4A3Y4nAFDka8M=", "Uq/Abn3Ra6ne2xdnk7sOOZViBj42VJ/3gg9pMLI9mow=", "GQ6Y2XcV1t2TPhMy1xjMWSxvaPmNToyMyk0XUsSbqWo=", "frK40kyq/tmhH2bS7/iXps4PDTIPRDyCL9NbZvYEh5c=", "m2fNfzthooZhDX45ZuwNUGDnLpzlBUn9SVYKy/TV/wg=", "y0DfjLPENmcaeUUn27ZsqNite0onXuxMuSu5YTnBo+M=", "ztab+ur95gWhDscTsCp32aAaZwh83ef6xMb+8zHwqck=", "A1VZ0i+64lS2wmiAhY08hAvL8X1Hdxf6SBqfryV2jSk=", "ZkwQGSQM8mui2qLa6ql3Fa3pmfvUsV7MyBUL1lfnTzQ=", "8IXGwe/5NqEqgXoppMLhGSSgrBa5c1hzit1Xwa8jzz4=", "eIqPkeszClIDnMjKI2PPhWjnJZIj/75erb2FSTpVsxM=", "d7DKuQH/W+LWOCqg0KqVXmK/lE74FU7T3cB8AEQ5tRc=", "KLuLJ5DJT/l8RTh7GPCH8hmwd7Rg1+Ki5OP0BGtqrVQ=", "xD1UcNoop/B4PsZy1+znQnrwVv7uhiyi6hvTuuO+6WU=", "gBGbDGUPDHgl+B5eNt1xLaeyiCUqixbsNnWEN8vtdCo=", "1K7+pCVaYwCGV1dXE1BUGx2AkERtQxmMyAjbRvw1LSo=", "rLK3NWdYRHO8dkqC1Niq9KsPolM8DuCnedLR5HRV4Q8=", "aPggXBElMZSRI0hlfFK7BuUJmrh3682LsZ+cTq0yMrc=", "COnz8bvTynbxEtD3DQ1HVbFj48Cea93Rncj002tbIIs="], "CachedAssets": {"s2qEi1RJ3EUbladsdBz4q+e0lJ5dFpcOc6sxHB2R1SU=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\d9np851p5q-khy4lop6wu.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "app#[.{fingerprint=khy4lop6wu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "596e5mu3rg", "Integrity": "KfTtGgOr4gFqWP6bDC3erwKnY0fchsCFGM8SFQeyGO0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\app.css", "FileLength": 1571, "LastWriteTime": "2025-06-12T14:02:33.0073222+00:00"}, "jDRZTgTACTtzOpT2ze8/dlMbGncjZ2IDyFx1VAsq+a4=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\ju4js0pm6f-bqjiyaj88i.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-12T14:02:32.9977693+00:00"}, "rrEm+gFADZ1xpj2WHarc04zmY9D7A8gHaQu70Ug7n9Q=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\jvge2q1mka-c2jlpeoesf.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-12T14:02:33.0271965+00:00"}, "BO3j/ydVLHVPYB7WQkUK27+tWI2ZRfW0t6hxcQN0FFo=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\6m7rrefwnf-erw9l3u2r3.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-12T14:02:32.9972623+00:00"}, "NCg3Ky39e5rhLISfzwBmtYigDJdTd3mcoz0531ZfsuY=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\j4xhibd0ek-aexeepp0ev.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-12T14:02:33.0003192+00:00"}, "VczgRVCdFnbtQrNhInHWyOeTf5+R/zcBRYX+IVQoy7c=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\djt8jbvssv-d7shbmvgxk.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-12T14:02:33.0133232+00:00"}, "x0FYM4AMKAHCMOjzRpDvGiH+VF7GfQzrFuY30+IH0tE=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\oa7cebvrr1-ausgxo2sd3.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-12T14:02:33.0053224+00:00"}, "tzCsDvTwZvS3x014P5UF0WPR2BWAERweYJcoTI+FM0c=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\clv14ko3hy-k8d9w2qqmf.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-12T14:02:33.0003192+00:00"}, "KOEkp7LDA12FKIQCuYbnCKmqss7YeaPBNCjKd/T0lTA=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\a8yf6lqyfm-cosvhxvwiu.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-12T14:02:33.0379645+00:00"}, "39ieHgRGDq+py5GJDyz8+2viH/RF1ZnIkzlSrIInwSM=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\te0mhhryfl-ub07r2b239.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-12T14:02:33.0053224+00:00"}, "jhfG11kXZTAMpyj0l9/Z2nGDeSB9qSxeLSZcSPS+4R4=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\wjnkm1p5qo-fvhpjtyr6v.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-12T14:02:33.0033146+00:00"}, "5sv1VI73BarRnvc/sPbcGNNtQ1mbz4SDw2o3SbuwCfg=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\k2krj4r7ce-b7pk76d08c.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-12T14:02:33.0133232+00:00"}, "FhsRn1Nt1zmLqCIffFxpzomYL8YD3s+4jVv5vEoWnj4=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\8ljgoyn5az-fsbi9cje9m.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-12T14:02:33.0053224+00:00"}, "5SgSt2hH/q0LncHz0EPTFscxrnh6imKNOebL1t+wMRs=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\1u59o4t8e7-rzd6atqjts.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-12T14:02:33.0063222+00:00"}, "JJiJ6QvPLkCyaoZNybrUXGkEgbXyTdE+t5B605tWNXQ=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\h7agyzv0xo-ee0r1s7dh0.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-12T14:02:33.0198421+00:00"}, "aF+7nWfZ0AGZOjGQOAZqBfkmXfuBvh2vaYAKXzNIo8A=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\i406opz1b3-dxx9fxp4il.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-12T14:02:33.011323+00:00"}, "I15D9R9RiCu/RqpjP9PopIWqVwN18DGVAUaxGMyP0Qo=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\k12xxh8ni7-jd9uben2k1.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-12T14:02:33.0053224+00:00"}, "9wZnHZIt0AWeMy276zevOWAJPP542Xv9uGioQcfLmmI=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\t5n7sfn5ai-khv3u5hwcm.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-12T14:02:33.017842+00:00"}, "687gpQ/3X47yrJ4TgtdnCcPtAwRtS6u1/WWxqBAtwUo=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\67nsxcsrod-r4e9w2rdcm.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-12T14:02:33.011323+00:00"}, "DvH7F2gUlVK7j5vryy1+mxWgJJFexuUPbhgZb9C4t2M=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\8r08sa36nw-lcd1t2u6c8.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-12T14:02:33.0073222+00:00"}, "ZIy76R3KJ1ucSfdN3z05G5QRGLLtTeNptWzr+bhoz00=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\zk0skgq6f4-c2oey78nd0.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-12T14:02:33.0198421+00:00"}, "JcI1sZGb5yGWounLafwSOG1smG4Zx3nVCOZj2+71o4Y=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\bjrx2pw9ay-tdbxkamptv.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-12T14:02:33.0053224+00:00"}, "KMnSdABkWoOmYbJZS7/8g6RuzpHhXiUu6NVGYVK1d/Q=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\d5l2avzm1q-j5mq2jizvt.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-12T14:02:33.0545007+00:00"}, "dZOZCHziptVzIFMdpdGefSAhc8Hf5by/mVVXFgJSBgw=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\a1ar09264q-06098lyss8.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-12T14:02:33.0198421+00:00"}, "4y/26zwWNxKAmekP0ULpV+LJxaD5PwP9eRqF8nzP6No=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\nujj3ouatu-nvvlpmu67g.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-12T14:02:33.0143236+00:00"}, "InHhB16R83NelO8spAbzRU0RIzZ+9kbQdlbZfigM5XU=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\tlkdmfq6pl-s35ty4nyc5.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-12T14:02:33.0033146+00:00"}, "2KPCrAaeoGPDvvdqMc1Inqd2OZlJ0oKIUrP6sUNohzQ=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\10oh4p0r7o-pj5nd1wqec.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-12T14:02:33.0459725+00:00"}, "AF41EuusBXiCzPekckR5+2j3EcyBScLnLM2TcL7Jbrg=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\tvboi8w8ut-46ein0sx1k.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-12T14:02:33.0379645+00:00"}, "Zhqsf7rMoGVC1Z0GSumW/cLNItF/em4A3Y4nAFDka8M=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\eiewjd5rn7-v0zj4ognzu.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-12T14:02:33.0198421+00:00"}, "Uq/Abn3Ra6ne2xdnk7sOOZViBj42VJ/3gg9pMLI9mow=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\k4ewoorp5v-37tfw0ft22.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-12T14:02:33.0208408+00:00"}, "GQ6Y2XcV1t2TPhMy1xjMWSxvaPmNToyMyk0XUsSbqWo=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\vj0wrtktl2-hrwsygsryq.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-12T14:02:33.0223514+00:00"}, "frK40kyq/tmhH2bS7/iXps4PDTIPRDyCL9NbZvYEh5c=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\1ydg2eumfv-pk9g2wxc8p.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-12T14:02:33.009322+00:00"}, "m2fNfzthooZhDX45ZuwNUGDnLpzlBUn9SVYKy/TV/wg=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\tj8x1njp8l-ft3s53vfgj.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-12T14:02:33.0399639+00:00"}, "y0DfjLPENmcaeUUn27ZsqNite0onXuxMuSu5YTnBo+M=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\5nlzt130no-6cfz1n2cew.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-12T14:02:33.0340615+00:00"}, "ztab+ur95gWhDscTsCp32aAaZwh83ef6xMb+8zHwqck=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\b2bhhp143f-6pdc2jztkx.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-12T14:02:33.0188442+00:00"}, "A1VZ0i+64lS2wmiAhY08hAvL8X1Hdxf6SBqfryV2jSk=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\1vdys0vccg-493y06b0oq.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-12T14:02:33.0198421+00:00"}, "ZkwQGSQM8mui2qLa6ql3Fa3pmfvUsV7MyBUL1lfnTzQ=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\ihvrlgyn3a-iovd86k7lj.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-12T14:02:33.0292069+00:00"}, "8IXGwe/5NqEqgXoppMLhGSSgrBa5c1hzit1Xwa8jzz4=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\mj2vor6q89-vr1egmr9el.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-12T14:02:33.0524943+00:00"}, "eIqPkeszClIDnMjKI2PPhWjnJZIj/75erb2FSTpVsxM=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\gz8gqtvxzd-kbrnm935zg.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-12T14:02:33.0459725+00:00"}, "d7DKuQH/W+LWOCqg0KqVXmK/lE74FU7T3cB8AEQ5tRc=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\zdp3yf1tb0-jj8uyg4cgr.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-12T14:02:33.0123229+00:00"}, "KLuLJ5DJT/l8RTh7GPCH8hmwd7Rg1+Ki5OP0BGtqrVQ=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\ssoiz1k1xn-y7v9cxd14o.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-12T14:02:33.0660245+00:00"}, "xD1UcNoop/B4PsZy1+znQnrwVv7uhiyi6hvTuuO+6WU=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\9ehbva6xp5-notf2xhcfb.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-12T14:02:33.0223514+00:00"}, "gBGbDGUPDHgl+B5eNt1xLaeyiCUqixbsNnWEN8vtdCo=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\hanau6nko0-h1s4sie4z3.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-12T14:02:33.0103227+00:00"}, "1K7+pCVaYwCGV1dXE1BUGx2AkERtQxmMyAjbRvw1LSo=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\y1jlw6svgx-63fj8s7r0e.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-12T14:02:33.0821489+00:00"}, "rLK3NWdYRHO8dkqC1Niq9KsPolM8DuCnedLR5HRV4Q8=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\5s56d4qfu2-0j3bgjxly4.gz", "SourceId": "BlueIQ.Web", "SourceType": "Discovered", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-12T14:02:33.0251877+00:00"}, "aPggXBElMZSRI0hlfFK7BuUJmrh3682LsZ+cTq0yMrc=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\ipgmga1rt0-7skqqv3n3p.gz", "SourceId": "BlueIQ.Web", "SourceType": "Computed", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "BlueIQ.Web#[.{fingerprint=7skqqv3n3p}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BlueIQ.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vfr4vkloxr", "Integrity": "6wLiYCsroEQ0IoX8D1QZCrRi3amRAJeXO//tOJB6zeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BlueIQ.Web.styles.css", "FileLength": 1780, "LastWriteTime": "2025-06-12T14:02:32.998291+00:00"}, "COnz8bvTynbxEtD3DQ1HVbFj48Cea93Rncj002tbIIs=": {"Identity": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\ykr8rjwelr-7skqqv3n3p.gz", "SourceId": "BlueIQ.Web", "SourceType": "Computed", "ContentRoot": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlueIQ.Web", "RelativePath": "BlueIQ.Web#[.{fingerprint=7skqqv3n3p}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BlueIQ.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vfr4vkloxr", "Integrity": "6wLiYCsroEQ0IoX8D1QZCrRi3amRAJeXO//tOJB6zeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BlueIQ.Web.bundle.scp.css", "FileLength": 1780, "LastWriteTime": "2025-06-12T14:02:33.0459725+00:00"}}, "CachedCopyCandidates": {}}