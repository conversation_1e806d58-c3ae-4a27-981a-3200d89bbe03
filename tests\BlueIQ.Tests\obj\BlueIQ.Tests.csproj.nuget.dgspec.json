{"format": 1, "restore": {"C:\\_Dev\\Projects\\BlueIQ-BMAD\\tests\\BlueIQ.Tests\\BlueIQ.Tests.csproj": {}}, "projects": {"C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Api\\BlueIQ.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Api\\BlueIQ.Api.csproj", "projectName": "BlueIQ.Api", "projectPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Api\\BlueIQ.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Data\\BlueIQ.Data.csproj": {"projectPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Data\\BlueIQ.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[8.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Data\\BlueIQ.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Data\\BlueIQ.Data.csproj", "projectName": "BlueIQ.Data", "projectPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Data\\BlueIQ.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "C:\\_Dev\\Projects\\BlueIQ-BMAD\\tests\\BlueIQ.Tests\\BlueIQ.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\tests\\BlueIQ.Tests\\BlueIQ.Tests.csproj", "projectName": "BlueIQ.Tests", "projectPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\tests\\BlueIQ.Tests\\BlueIQ.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\tests\\BlueIQ.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Api\\BlueIQ.Api.csproj": {"projectPath": "C:\\_Dev\\Projects\\BlueIQ-BMAD\\src\\BlueIQ.Api\\BlueIQ.Api.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}