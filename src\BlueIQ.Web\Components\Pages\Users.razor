@page "/users"
@using BlueIQ.Api.Models
@using BlueIQ.Web.Components.Custom

<PageTitle>Users - BlueIQ</PageTitle>

<h1>Users</h1>

<p>This page demonstrates the UserCard component pattern.</p>

@if (users == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <div class="row">
        @foreach (var user in users)
        {
            <div class="col-md-6 col-lg-4">
                <UserCard User="user" />
            </div>
        }
    </div>
}

@code {
    private List<UserDto>? users;

    protected override async Task OnInitializedAsync()
    {
        // Simulate loading users - in a real app, this would call the API
        await Task.Delay(500); // Simulate network delay
        
        users = new List<UserDto>
        {
            new UserDto
            {
                Id = 1,
                Email = "<EMAIL>",
                FirstName = "Admin",
                LastName = "User",
                CreatedAt = DateTime.UtcNow.AddMonths(-6)
            },
            new UserDto
            {
                Id = 2,
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe",
                CreatedAt = DateTime.UtcNow.AddMonths(-3)
            },
            new UserDto
            {
                Id = 3,
                Email = "<EMAIL>",
                FirstName = "Jane",
                LastName = "Smith",
                CreatedAt = DateTime.UtcNow.AddMonths(-1)
            }
        };
    }
}
